from collections import defaultdict, namedtuple, OrderedDict, Counter
from FlagEmbedding import BGEM3FlagModel
from openpyxl import load_workbook
from sklearn.cluster import KMeans
from sklearn.exceptions import ConvergenceWarning
from sklearn.feature_extraction.text import TfidfVectorizer
from transformers import AutoModelForCausalLM, AutoTokenizer
from typing import List, Dict, Optional, Set, Union, Tuple

import csv
import heapq
import jieba.analyse
import json
import math
import numpy as np
import os
import pandas as pd
import re
import sqlite3
import torch
import warnings


# --- 总体参数配置 ---
# DEVICE = "cuda" if torch.cuda.is_available() else "cpu"  # 启用CUDA
# model_embedding = BGEM3FlagModel('BAAI/bge-m3', use_fp16=True)  # 词嵌入模型
BATCH_SIZE = 1  # 批量处理大小
KEY_MAX_LENGTH = 48  # 输入长度限制(关键词、标签)
INPUT_MAX_LENGTH = 8192  # 输入长度限制(长文本)
global RERANKER_INITTIAL_RESULTS  # 模型直出结果
global RERANKER_FLAG_OTRO  # 模型"其他标签"FLAG
# --- 聚类参数配置 ---
warnings.filterwarnings("ignore", category=ConvergenceWarning)  # 取消告警
# --- 本地模型配置 ---
# 使用模型管理器
from model_manager import get_embedding_model, get_reranker_model, get_device

# 标签分类器
class LabelPredictor:
    """
    打标器类；

    Args:
        model1        : SINGLE RERANKER 全渠道标签选取
        tokenizer1    : SINGLE RERANKER TOKENIZER
        layer1        : SINGLE RERANKER 激活层数
        beam_size     : BEAM SEARCH 搜索宽度
        max_depth     : BEAM SEARCH 搜索深度

    Func:
        predict       : 入参为工单文本，出参为全局重排标签列表
        beam_search   : 入参为工单文本，出参为逐级打标(对应标签/得分)列表
        _score_level  : 入参为工单文本、回溯路径和候选标签，出参为(得分/对应标签)列表
        rerank_full_path
                      : 入参为工单文本和候选标签Top32，出参为全局重排标签列表
    """

    def __init__(self, model, tokenizer, layer_model, beam_size, max_depth, device="cuda", ):
        self.model = model  # SINGLE RERANKER 全渠道标签选取
        self.tokenizer = tokenizer
        self.layer = layer_model  # SINGLE RERANKER 激活层数
        self.device = device
        self.beam_size = beam_size  # BEAM SEARCH 搜索宽度
        self.max_depth = max_depth  # BEAM SEARCH 搜索深度
        global RERANKER_INITTIAL_RESULTS

    def predict(self, text: str) -> List[str]:
        global RERANKER_INITTIAL_RESULTS
        candidates = self.beam_search(text)  # SINGLE RERANKER TOP32
        candidates_new = self.beam_result(candidates)
        RERANKER_INITTIAL_RESULTS = candidates_new
        return self.rerank_full_path_similarity(text, candidates)
        # return candidates_new
        # return self.rerank_full_path(text, candidates) # PATH   RERANKER TOP32


    def beam_result(self, candidates: List[Tuple[float, List[float], List[str]]]) -> List[str]:
        final_result = []
        for idx, (score, score_list, path) in enumerate(candidates):
            temp_path = '-'.join(path)
            final_result.append(temp_path)
        return final_result

    def beam_search(self, text: str) -> List[Tuple[float, List[float], List[str]]]:
        # 初始化第一层
        # print(f"""第1层""")
        level1_tags = query_by_level(1)
        heap = self._score_level(text, [], level1_tags)

        # 逐层扩展
        for current_level in range(2, self.max_depth + 1):
            # print(f"""第{current_level}层""")
            new_heap = []
            for score, score_list, path in heapq.nlargest(pow(self.beam_size, current_level), heap):
                parent_path = '/'.join(path)
                candidates = query_by_ancestor(parent_path, current_level)
                if candidates:
                    # 生成候选对
                    candidate_pairs = [
                        [text, tag]
                        for tag in candidates
                    ]

                    # 执行模型推理
                    with torch.no_grad():
                        inputs = get_inputs(candidate_pairs, self.tokenizer).to(self.device)
                        all_scores = self.model(**inputs, return_dict=True, cutoff_layers=[self.layer])
                        scores_list = [s[:, -1].view(-1, ).float() for s in all_scores[0]]
                        combined_scores = torch.cat(scores_list, dim=0) if len(scores_list) > 1 else scores_list[0]

                    # 扩展路径
                    for tag, s in zip(candidates, combined_scores):

                        if (current_level == 3):
                            # if (get_sparse_similarity(text, [tag])[0]>=0.07):
                            score_list_temp = score_list.copy()
                            new_path = path + [tag]
                            score_list_temp.append(s.item())
                            new_score = score + s.item()
                            # print(f"""当前路径：{new_path};当前分数：{s.item()};历史累计分数：{score};当前累计分数:{new_score}""")
                            heapq.heappush(new_heap, (new_score, score_list_temp, new_path))
                        else:
                            score_list_temp = score_list.copy()
                            new_path = path + [tag]
                            score_list_temp.append(s.item())
                            new_score = score + s.item()
                            # print(f"""当前路径：{new_path};当前分数：{s.item()};历史累计分数：{score};当前累计分数:{new_score}""")
                            heapq.heappush(new_heap, (new_score, score_list_temp, new_path))
                else:
                    # print(f"""candidates 为空；parent_path:{parent_path};current_level:{current_level}""")
                    heapq.heappush(new_heap, (score, score_list, path))  # 若无新增路径节点，保留历史节点
                    # print(f"路径 {path} 无子节点，保留至层级 {current_level}")
            heap = new_heap
            if not heap:
                break

        return heapq.nlargest(32, heap, key=lambda x: x[0])

    def _score_level(self, text: str, parent_path: List[str], candidates: List[str]) -> List[
        Tuple[float, List[float], List[str]]]:
        # 生成候选对
        candidate_pairs = [[text, tag] for tag in candidates]

        # rerank
        with torch.no_grad():
            inputs = get_inputs(candidate_pairs, self.tokenizer).to(self.device)
            all_scores = self.model(**inputs, return_dict=True, cutoff_layers=[self.layer])
            scores_list = [s[:, -1].view(-1, ).float() for s in all_scores[0]]
            combined_scores = torch.cat(scores_list, dim=0) if len(scores_list) > 1 else scores_list[0]
        # for tag, s in zip(candidates, combined_scores):
        #    print(f"""当前路径：{tag};当前分数：{s.item()};""")
        return [(s.item(), [s.item()], [tag]) for tag, s in zip(candidates, combined_scores)]

    def rerank_full_path_similarity(self, text: str, candidates: List[Tuple[float, List[float], List[str]]]) -> List[
        str]:
        """全局重排序"""
        global RERANKER_FLAG_OTRO
        RERANKER_FLAG_OTRO = 0
        score_flag = 0
        result_total = []
        # 生成候选对
        # candidate_pairs = [(text, '-'.join(path)) for score, score_list, path in candidates]
        candidtate_paths = ['/'.join(path) for score, score_list, path in candidates]
        for path in candidtate_paths:
            hit_score = 0

            tag1_rules = []
            score_tag1_rules = 0

            tag3_keywords = []
            score_tag3_keywords = 0

            tag4_rules = []
            tag4_keywords = []
            score_tag4_rules = 0
            score_tag4_keywords = 0

            tag5_rules = []
            tag5_keywords = []
            score_tag5_rules = 0
            score_tag5_keywords = 0

            list_tag = path.split('/')
            tag1_rules = parse_db_similarity(query_rule_by_ancestor(list_tag[0], list_tag[0]))
            score_tag1_rules = get_rule_similarity(text, tag1_rules)
            # print(f'''部门标签为：{list_tag[0]}；规则为：{tag1_rules}；规则得分为：{score_tag1_rules}''')
            tag3_keywords = parse_db_similarity(query_keywords_by_ancestor('/'.join(list_tag[0:3]), list_tag[2]))
            score_tag3_keywords = get_keyword_similarity(text, tag3_keywords)

            tag4_keywords = parse_db_similarity(query_keywords_by_ancestor('/'.join(list_tag[0:4]), list_tag[3]))
            score_tag4_keywords = get_keyword_similarity(text, tag4_keywords)
            tag4_rules = parse_db_similarity(query_rule_by_ancestor('/'.join(list_tag[0:4]), list_tag[3]))
            score_tag4_rules = get_rule_similarity(text, tag4_rules)

            tag5_rules = parse_db_similarity(query_rule_by_ancestor('/'.join(list_tag[0:]), list_tag[4]))
            score_tag5_rules = get_rule_similarity(text, tag5_rules)
            # score_tag5_rules = get_keyword_similarity(text,tag5_rules)
            # print(f'''原因标签为：{list_tag[4]}；规则为：{tag5_rules}；规则得分为：{score_tag5_rules}''')
            tag5_keywords = parse_db_similarity(query_keywords_by_ancestor('/'.join(list_tag[0:]), list_tag[4]))
            score_tag5_keywords = get_keyword_similarity(text, tag5_keywords)
            for keyword in tag5_keywords:
                temp = clean_special_char(keyword)
                if (temp in text) and (temp != ''):
                    hit_score += 1
            score_flag += score_tag5_keywords
            # print(f'''原因标签为：{list_tag[4]}；关键词为：{tag5_keywords}；关键词得分为：{hit_score}''')
            result_total.append(
                ['-'.join(list_tag), score_tag1_rules, score_tag3_keywords, score_tag4_rules, score_tag4_keywords,
                 score_tag5_rules, score_tag5_keywords, hit_score])
        if score_flag < 3.2:
            RERANKER_FLAG_OTRO = 1

        result_total_sort = sorted(result_total, key=lambda x: (x[1], x[2], x[3] + x[4]), reverse=True)

        groups = {}
        for item in result_total_sort:
            group_key = (item[1], item[2], item[3] + item[4])
            groups.setdefault(group_key, []).append(item)

        final_sorted = []
        for key, group_items in groups.items():
            # 1. 计算五级规则排名
            rule_scores = [item[5] for item in group_items]
            unique_rule_scores = sorted(set(rule_scores), reverse=True)
            rule_rank_dict = {score: rank + 1 for rank, score in enumerate(unique_rule_scores)}

            # 2. 计算五级关键词排名
            keyword_scores = [item[6] for item in group_items]
            unique_keyword_scores = sorted(set(keyword_scores), reverse=True)
            keyword_rank_dict = {score: rank + 1 for rank, score in enumerate(unique_keyword_scores)}

            # 3. 计算平均排名并添加临时字段
            ranked_items = []
            for item in group_items:
                rule_rank = rule_rank_dict[item[5]]
                keyword_rank = keyword_rank_dict[item[6]]
                avg_rank = (rule_rank + keyword_rank) / 2.0
                # 保留原始数据并添加临时字段
                ranked_items.append((item, avg_rank))

            ranked_items.sort(key=lambda x: (
                x[0][7] == 0,  # 关键词为0的放后面
                -x[0][7],  # 关键词得分降序
                x[1]  # 平均排名升序
            ))

            for item, _ in ranked_items:
                final_sorted.append(item)

        return sorted(final_sorted, key=lambda x: x[2], reverse=True)


def t4gger4040(file_path, worksheet_list, output_name):
    """
        新版本的标签预测函数，读取T列和U列拼接作为context，将结果写入V列

        Args:
            file_path: Excel文件路径
            worksheet_list: 工作表名称列表
            output_name: 输出文件名（不含扩展名）
        """
    global RERANKER_INITTIAL_RESULTS
    global RERANKER_FLAG_OTRO

    # 确保输出目录存在
    output_dir = "./output"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"创建输出目录: {output_dir}")

    # 通过模型管理器获取模型
    model_reranker_single, tokenizer_reranker_single = get_reranker_model()

    predictor = LabelPredictor(model_reranker_single, tokenizer_reranker_single, 40, 2, 5, device=get_device())

    # 使用新的读取函数获取T列和U列拼接的数据
    result_train_set = get_testing_set_with_tu_columns(file_path, worksheet_list)

    # 加载原始Excel文件用于写入结果
    workbook = load_workbook(file_path)

    count_case = 0

    print(f"开始处理 {len(result_train_set)} 行数据...")

    for row_num, context, t_value, u_value, worksheet_name in result_train_set:
        if context != '':
            count_case += 1  # 统计总案例个数
            result_path = predictor.predict(context)
            temp_result = []

            print(f"""""")
            for i in result_path:
                print(f"""label:{i[0]};1r:{i[1]};3k:{i[2]};4rk:{i[3] + i[4]};5r:{i[5]};5k:{i[6]};5hit:{i[7]};""")
                temp_result.append(i[0])

            print(f"Case{count_case};Row {row_num};Context: {context[:100]}...")  # 只显示前100个字符

            # 将结果写入V列（第22列）
            sheet = workbook[worksheet_name]
            # 取最佳预测结果
            best_label = temp_result[0] if temp_result else ""
            sheet.cell(row=row_num, column=22, value=best_label)  # V列是第22列

            for idx, result in enumerate(RERANKER_INITTIAL_RESULTS):
                print(f"RERANKER_SINGLE:Top {idx};label {result};")
            for idx, result in enumerate(temp_result):
                print(f"RERANKER_PATH  :Top {idx};label {result};")

            # 每处理10个案例显示一次进度
            if count_case % 10 == 0:
                print(f"已处理 {count_case} 个案例...")

    # 保存修改后的Excel文件
    output_file_path = f"./output/{output_name}.xlsx"
    workbook.save(output_file_path)
    print(f"结果已保存到: {output_file_path}")
    print(f"总共处理了 {count_case} 个案例")

# 数据库测试
def test_db():
    print(
        parse_db_similarity(query_rule_by_ancestor('APP运营室/APP功能/电子发票/订单完成/开票信息错误', '开票信息错误')))
    print(parse_db_similarity(
        query_keywords_by_ancestor('APP运营室/APP功能/电子发票/订单完成/开票信息错误', '开票信息错误')))
    print(parse_db_similarity(query_rule_by_ancestor('APP运营室', 'APP运营室')))
    print(parse_db_similarity(query_keywords_by_ancestor('APP运营室', 'APP运营室')))

    print(parse_db_similarity(
        query_rule_by_ancestor('生产交付室/号卡业务/无忧卡/激活问题/激活问题：省内报错', '激活问题：省内报错')))
    print(parse_db_similarity(
        query_keywords_by_ancestor('生产交付室/号卡业务/无忧卡/激活问题/激活问题：省内报错', '激活问题：省内报错')))
    print(parse_db_similarity(query_rule_by_ancestor('生产交付室', '生产交付室')))
    print(parse_db_similarity(query_keywords_by_ancestor('生产交付室', '生产交付室')))

    print(parse_db_similarity(
        query_rule_by_ancestor('生产交付室/号卡业务/无忧卡/订单审核/Iccid校验失败', 'Iccid校验失败')))
    print(parse_db_similarity(
        query_keywords_by_ancestor('生产交付室/号卡业务/无忧卡/订单审核/Iccid校验失败', 'Iccid校验失败')))


# --- Reranker模型调用 ---
# 提示词拼接与向量化
def get_inputs(pairs, tokenizer, prompt=None, max_length=1536):
    if prompt is None:
        prompt = "请根据工单内容打标签"
    sep = "\n"
    prompt_inputs = tokenizer(prompt,
                              return_tensors=None,
                              add_special_tokens=False)['input_ids']
    sep_inputs = tokenizer(sep,
                           return_tensors=None,
                           add_special_tokens=False)['input_ids']
    inputs = []
    for query, passage in pairs:
        query_inputs = tokenizer(f'工单内容：{query}',
                                 return_tensors=None,
                                 add_special_tokens=False,
                                 max_length=max_length * 3 // 4,
                                 truncation=False)  # 防止截断
        passage_inputs = tokenizer(f'标签：{passage}',
                                   return_tensors=None,
                                   add_special_tokens=False,
                                   max_length=max_length,
                                   truncation=False)  # 防止截断
        item = tokenizer.prepare_for_model(
            [tokenizer.bos_token_id] + query_inputs['input_ids'],
            sep_inputs + passage_inputs['input_ids'],
            truncation='only_second',
            max_length=max_length,
            padding=False,
            return_attention_mask=False,
            return_token_type_ids=False,
            add_special_tokens=False
        )
        item['input_ids'] = item['input_ids'] + sep_inputs + prompt_inputs
        item['attention_mask'] = [1] * len(item['input_ids'])
        inputs.append(item)
    return tokenizer.pad(
        inputs,
        padding=True,
        max_length=max_length + len(sep_inputs) + len(prompt_inputs),
        pad_to_multiple_of=8,
        return_tensors='pt',
    )


# 匹配得分
# 读取EXCEL数据
# 新数据标签列号1，2;
def get_testing_set(file_path, tag_list_input, min_col, max_col):
    tag_list = tag_list_input

    # 加载 Excel 文件
    workbook = load_workbook(file_path, data_only=True)
    results = []
    for tag in tag_list:
        sheet = workbook[tag]
        # 存储结果的列表（每个元素是列的元组）

        # 遍历从第二行开始的每一行（openpyxl 行号从 1 开始）
        for row in sheet.iter_rows(min_row=2, min_col=min_col, max_col=max_col, values_only=True):
            tag_value, context_value = row  # 解包所有列的值
            processed_context_general = extract_content_distribution(context_value)
            processed_context_additional = clean_special_char(processed_context_general)
            results.append((tag_value, processed_context_additional))
    return results


# 读取EXCEL数据 - 新版本：读取T列和U列拼接作为context
def get_testing_set_with_tu_columns(file_path, worksheet_list):
    """
    读取Excel文件，取T列和U列拼接作为context_value

    Args:
        file_path: Excel文件路径
        worksheet_list: 工作表名称列表

    Returns:
        results: 包含(行号, 拼接后的context, 原始T列值, 原始U列值)的列表
    """
    # 加载 Excel 文件
    workbook = load_workbook(file_path, data_only=True)
    results = []

    for worksheet_name in worksheet_list:
        sheet = workbook[worksheet_name]

        # 遍历从第二行开始的每一行（openpyxl 行号从 1 开始）
        # T列是第20列，U列是第21列
        for row_num, row in enumerate(sheet.iter_rows(min_row=2, min_col=20, max_col=21, values_only=True), start=2):
            t_value, u_value = row  # T列和U列的值

            # 处理空值
            t_value = str(t_value) if t_value is not None else ""
            u_value = str(u_value) if u_value is not None else ""

            # 拼接T列和U列的值作为context
            context_value = t_value + u_value

            if context_value.strip():  # 只处理非空的context
                processed_context_general = extract_content_distribution(context_value)
                processed_context_additional = clean_special_char(processed_context_general)
                results.append((row_num, processed_context_additional, t_value, u_value, worksheet_name))

    return results


# EMBEDDING模型调用
# 规则相似度
def get_rule_similarity(text, rules):
    """
    """
    score = 0.0
    if rules == [''] or rules == []:
        return score
    score = get_similarity(text, rules)
    return score


# 关键词相似度
def get_keyword_similarity(text, keywords):
    """
    """
    score = 0.0
    if keywords == [''] or keywords == []:
        return score
    score = get_sparse_similarity(text, keywords)
    return score


# 关键词抓取
def parse_keywords_csv(csv_path, length):
    """
    解析关键词CSV文件并提取前N个关键词

    Args:
        csv_path      : 关键词文件路径
        length        : 关键词截断个数
    Returns:
        tag_keywords  : 字典，格式为{'标签链':[关键词1,关键词2,...]}

    """
    tag_keywords = {}
    cutoff = math.floor(60 / length)
    with open(csv_path, 'r', encoding='utf-8') as f:
        reader = csv.reader(f)
        for row in reader:
            if len(row) < 2:
                continue

            tag_level = row[0]
            keywords_str = row[1]
            tags = tag_level.split('-')
            if len(tags) == length:
                # 分割关键词并取前10个
                keywords = keywords_str.split('-')[:cutoff]
                tag_keywords[tag_level] = keywords

    return tag_keywords


# 相似度计算
def get_similarity(input_query, keyword_list):
    similarities = []
    dense_value = dense_retrieval_batch(input_query, keyword_list)
    # sparse_value = sparse_retrieval_batch(input_query,keyword_list)
    multi_value = multival_retrieval_batch(input_query, keyword_list)
    for i, keyword in enumerate(keyword_list):
        weight_value = dense_value[i] * 1 + multi_value[i] * 1
        # print(f"""关键词{keyword}相似度:稠密{dense_value[i]}|稀疏{sparse_value[i]}|多向量{multi_value[i]}|总分{weight_value}""")
        similarities.append(weight_value)
    # similarity_score = sum(similarities) / len(similarities)
    return max(similarities)


def get_sparse_similarity(input_query, keyword_list):
    similarities = []
    # dense_value = dense_retrieval_batch(input_query,keyword_list)
    sparse_value = sparse_retrieval_batch(input_query, keyword_list)
    # multi_value = multival_retrieval_batch(input_query,keyword_list)
    # for i, keyword in enumerate(keyword_list):
    # weight_value = sparse_value[i]
    # print(f"""关键词{keyword}相似度:稠密{dense_value[i]}|稀疏{sparse_value[i]}|多向量{multi_value[i]}|总分{weight_value}""")
    # similarities.append(weight_value)
    return max(sparse_value)


# 稠密检索
def dense_retrieval_batch(input1, input2):
    model_embedding = get_embedding_model()
    embeddings_1 = model_embedding.encode(input1,
                                          max_length=INPUT_MAX_LENGTH
                                          )['dense_vecs']
    embeddings_2 = model_embedding.encode(input2,
                                          max_length=INPUT_MAX_LENGTH
                                          )['dense_vecs']
    # compute the similarity scores
    s_dense = embeddings_1 @ embeddings_2.T
    return s_dense


# 多向量检索
def multival_retrieval_batch(input, keyword_list):
    input = [input]
    model_embedding = get_embedding_model()
    embeddings_1 = model_embedding.encode(input,
                                          max_length=INPUT_MAX_LENGTH,
                                          return_dense=True, return_sparse=True, return_colbert_vecs=True)
    embeddings_2 = model_embedding.encode(keyword_list,
                                          max_length=INPUT_MAX_LENGTH,
                                          return_dense=True, return_sparse=True, return_colbert_vecs=True)
    scores = []
    for i in range(len(keyword_list)):
        s_mul_10_20 = model_embedding.colbert_score(embeddings_1['colbert_vecs'][0],
                                                    embeddings_2['colbert_vecs'][i]).item()
        scores.append(s_mul_10_20)
    # print(f"关键词：{keyword} | ColBERT得分:{s_mul_10_20}")
    return scores


# 稀疏检索
def sparse_retrieval_batch(input, keyword_list):
    model_embedding = get_embedding_model()
    embeddings_1 = model_embedding.encode(input,
                                          max_length=INPUT_MAX_LENGTH,
                                          return_dense=True, return_sparse=True, return_colbert_vecs=False)
    embeddings_2 = model_embedding.encode(keyword_list,
                                          max_length=KEY_MAX_LENGTH,
                                          return_dense=True, return_sparse=True, return_colbert_vecs=False)
    # print(f"关键词：{keyword} | lexical得分:{lexical_scores}")
    lexical_scores = []
    for i in range(len(keyword_list)):
        # 提取当前 keyword 的 lexical_weights
        current_keyword_weights = {
            'lexical_weights': embeddings_2['lexical_weights'][i]  # 注意索引方式
        }

        # 计算 lexical 匹配分数
        score = model_embedding.compute_lexical_matching_score(
            embeddings_1['lexical_weights'],  # input 的权重
            current_keyword_weights['lexical_weights']  # 当前 keyword 的权重
        )
        lexical_scores.append(score)
    return lexical_scores


# --- 训练数据集拼接 ---
def training_data_set_conversion_cluster(data, output_json):
    """
    跟据提供标签对应的清洗数据提取正负样本并生成训练数据

    Args:
        data          : DataFrame，需包含'Label'和'Context'列

    Returns:
        output_json   : 正负样本训练数据，格式为-
                        {
                        "query": item['context'],
                        "pos": [abc],
                        "neg": other_abcs,
                        "prompt": "请根据工单内容打标签"
                        }

    """
    print(output_json)
    groups = defaultdict(list)
    for _, row in data.iterrows():
        tag = row['Label']
        parts = tag.split('-')
        if parts[0] == '生产与交付室':
            continue
        ab_key = '-'.join(parts[:2])
        abc_key = '-'.join(parts[:3])
        abcd_key = '-'.join(parts[:4])
        abcde_key = '-'.join(parts[:5])

        a_key = parts[0]
        b_part = parts[1]
        c_part = parts[2]
        d_part = parts[3]
        e_part = parts[4]

        groups[a_key].append({
            'ab_key': ab_key,
            'abc_key': abc_key,
            'abcd_key': abcd_key,
            'abcde_key': abcde_key,
            'a': a_key,
            'b': b_part,
            'c': c_part,
            'd': d_part,
            'e': e_part,
            'Context': row['Context']
        })

    with open(output_json, "w", encoding="utf-8") as f:
        max_len_neg = 0
        max_len_context = 0
        max_passage_length = 0
        count_step = 0
        for a_keyword, items in groups.items():
            length_neg = 0
            for item in items:
                ab_val = item['ab_key']
                abc_val = item['abc_key']
                abcd_val = item['abcd_key']
                abcde_val = item['abcde_key']
                a_val = item['a']
                b_val = item['b']
                c_val = item['c']
                d_val = item['d']
                e_val = item['e']
                length_passage = len(abcde_val)
                if length_passage > max_passage_length:
                    max_passage_length = length_passage
                negs = []
                other_as = query_by_level(1)
                for other_a in other_as:
                    if other_a == a_val:
                        continue
                    else:
                        negs.append(other_a)
                other_abs = []
                other_bs = query_by_ancestor(a_val, 2)
                for other_b in other_bs:
                    if other_b == b_val:
                        continue
                    else:
                        if str(output_json).find("path") != -1:
                            other_abs.append(f"""{a_val}-{other_b}""")
                        if str(output_json).find("single") != -1:
                            other_abs.append(other_b)
                negs.extend(other_abs)
                other_abcs = []
                other_cs = query_by_ancestor('/'.join(ab_val.split('-')), 3)
                for other_c in other_cs:
                    if other_c == c_val:
                        continue
                    else:
                        if str(output_json).find("path") != -1:
                            other_abcs.append(f"""{a_val}-{b_val}-{other_c}""")
                        if str(output_json).find("single") != -1:
                            other_abcs.append(other_c)
                negs.extend(other_abcs)
                other_abcds = []
                other_ds = query_by_ancestor('/'.join(abc_val.split('-')), 4)
                for other_d in other_ds:
                    if other_d == d_val:
                        continue
                    else:
                        if str(output_json).find("path") != -1:
                            other_abcds.append(f"""{a_val}-{b_val}-{c_val}-{other_d}""")
                        if str(output_json).find("single") != -1:
                            other_abcds.append(other_d)
                negs.extend(other_abcds)
                other_abcdes = []
                other_es = query_by_ancestor('/'.join(abcd_val.split('-')), 5)
                for other_e in other_es:
                    if other_e == e_val:
                        continue
                    else:
                        if str(output_json).find("path") != -1:
                            other_abcdes.append(f"""{a_val}-{b_val}-{c_val}-{d_val}-{other_e}""")
                        if str(output_json).find("single") != -1:
                            other_abcdes.append(other_e)
                negs.extend(other_abcdes)
                length_neg = len(negs)
                length_context = len(item['Context'])
                if length_context > max_len_context:
                    max_len_context = length_context
                if length_neg > max_len_neg:
                    max_len_neg = length_neg
                if length_neg != 0:
                    if str(output_json).find("single") != -1:
                        entry_abc = {
                            "query": item['Context'],
                            "pos": [a_val, b_val, c_val, d_val, e_val],
                            "neg": negs,
                            "prompt": "请根据工单内容打标签"
                        }
                        f.write(json.dumps(entry_abc, ensure_ascii=False) + "\n")
                        count_step += 1
                    if str(output_json).find("path") != -1:
                        entry_abc = {
                            "query": item['Context'],
                            "pos": [a_val, b_val, c_val, d_val, e_val, ab_val, abc_val, abcd_val, abcde_val],
                            "neg": negs,
                            "prompt": "请根据工单内容打标签"
                        }
                        f.write(json.dumps(entry_abc, ensure_ascii=False) + "\n")
                        count_step += 1
        print(f"""最大标签nge列表长度为：{max_len_neg}；最大标签长度：{max_passage_length}""")
        print(f"""最大工单内容长度为：{max_len_context}；目前epoch步长为：{count_step}""")


def bge_training_data_cluster(file_path, output_path):
    df = pd.read_csv(file_path)
    # 确保列名正确
    df.columns = ['Label', 'Context'] if len(df.columns) == 2 else df.columns
    df = df.dropna(subset=['Context'])  # 删除Context为空的行
    training_data_set_conversion_cluster(df, output_path)


# --- 关键词提取 ---
def keyword_extraction_textrank_custom(context, topK):
    # jieba.analyse.set_stop_words('./bunkr/filter.txt') # 加载自定义停用词表
    keywords = jieba.analyse.textrank(context, topK=topK,
                                      allowPOS=('n', 'nz', 'v', 'vd', 'vn', 'l', 'a', 'd'))  # TextRank关键词提取，词性筛选
    return keywords


def process_tag_levels(data):
    """
    跟据提供标签对应的清洗数据提取关键词并保存（词频排序）

    Args:
        data          : 包含待分组数据的DataFrame，需包含'Label'和'Context'列

    Returns:
        tag_level_keywords
                      : 嵌套的defaultdict(Counter)结构，格式为：
                           {"一级标签": Counter({"关键词1":频次, ...}),
                            "一级标签-二级标签": Counter(...),}

    """
    tag_level_keywords = defaultdict(Counter)

    for _, row in data.iterrows():
        tags = row['Label'].split('-')
        context = row['Context']
        keywords = keyword_extraction_textrank_custom(context, 20)
        current_tag = ""
        for i, tag in enumerate(tags):
            if i == 0:
                current_tag = tag
            else:
                current_tag = f"{current_tag}-{tag}"
            # 累加关键词频次到对应层级
            tag_level_keywords[current_tag].update(keywords)

    return tag_level_keywords


# 处理并保存关键词
def keyword4tag(file_path, output_name):
    """
    跟据提供标签对应的清洗数据提取关键词并保存（词频排序）

    Args:
        file_path     : 清洗数据文件
        output_name   : 关键词另存为命名

    Returns:
        None

    """
    df = pd.read_csv(file_path)
    # 确保列名正确
    df.columns = ['Label', 'Context'] if len(df.columns) == 2 else df.columns
    df = df.dropna(subset=['Context'])  # 删除Context为空的行

    tag_level_results = process_tag_levels(df)

    for tag_level, counter in tag_level_results.items():
        # 按频次降序，频次相同按字母顺序排序
        sorted_keywords = sorted(counter.items(), key=lambda x: (-x[1], x[0]))
        # keywords_with_freq = [f"{kw} ({count})" for kw, count in sorted_keywords]
        keywords_without_freq = [f"{kw}" for kw, count in sorted_keywords]
        # print(f"Tag Level: {tag_level}")
        # print(f"Keywords: {', '.join(keywords_with_freq)}\n")
        with open(f'''./output/{output_name}_keyword_analysis.csv''', 'a', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow([f"{tag_level}", f"{'-'.join(keywords_without_freq)}"])


# --- 聚类分析 ---
# 二级标签清洗数据预处理
def load_and_preprocess_data(file_path):
    """加载并预处理数据"""
    df = pd.read_csv(file_path)
    # 确保列名正确
    df.columns = ['Label', 'Context'] if len(df.columns) == 2 else df.columns
    df = df.dropna(subset=['Context'])  # 删除Context为空的行
    return df


# 二级标签提取
def extract_middle_label(label):
    """从标签中提取中间部分（第一个和第二个'-'之间的内容）"""
    if pd.isna(label):  # 处理label为空的情况
        return None
    parts = label.split('-')
    return parts[1] if len(parts) >= 3 else None


# 一级标签数据聚类分析
def cluster_all_rows(contexts, n_clusters=5):
    """
    基于一级标签对全部文本数据执行聚类分析

    Args:
        contexts      : 待聚类的文本列表，元素为字符串或NaN
        n_clusters    : 目标聚类数量

    Returns:
        clusters      : 聚类结果数组，形状为(n_samples,)，元素为各样本对应的簇编号（0~n_clusters-1）

    """
    # 自动处理缺失值（NaN转换为空字符串）
    contexts = [str(doc) if not pd.isna(doc) else '' for doc in contexts]

    # 使用TF-IDF进行文本向量化
    vectorizer = TfidfVectorizer(max_features=1000, min_df=2)
    X = vectorizer.fit_transform(contexts)

    # 使用KMeans聚类
    # 实际簇数不超过样本数-1
    actual_n_clusters = min(n_clusters, len(contexts) - 1)
    # TF-IDF保留最高频1000个词，忽略文档频率<2的词
    kmeans = KMeans(n_clusters=actual_n_clusters, random_state=42)
    clusters = kmeans.fit_predict(X)

    return clusters


# 二级标签数据分组
def cluster_by_middle_label(df):
    """
    按中间标签自动分组

    Args:
        df            : 包含待分组数据的DataFrame，需包含'Label'和'Context'列

    Returns:
        groups        : 字典结构 {完整标签: [(行索引1, 文本1), (行索引2, 文本2)]}

    """
    # 自动将中间标签相同的行分为一组
    groups = defaultdict(list)
    for idx, row in df.iterrows():
        middle_label = extract_middle_label(row['Label'])
        if middle_label:
            context = str(row['Context']) if not pd.isna(row['Context']) else ''
            groups[middle_label].append((idx, context))
    return groups


# 完整标签数据分组
def cluster_by_full_label(df):
    """
    按完整标签自动分组

    Args:
        df            : 包含待分组数据的DataFrame，需包含'Label'和'Context'列

    Returns:
        groups        : 字典结构 {完整标签: [(行索引1, 文本1), (行索引2, 文本2)]}

    """
    # 将标签完全相同的行分为一组
    groups = defaultdict(list)
    for idx, row in df.iterrows():
        context = str(row['Context']) if not pd.isna(row['Context']) else ''
        groups[row['Label']].append((idx, context))
    return groups


# 聚类分析
def perform_clustering_within_groups(grouped_data, n_clusters=3):
    """
    对分组后的数据在每组内部进行独立聚类分析

    Args：
        grouped_data            : 字典结构，格式为 {组名: [(索引1, 文本1), (索引2, 文本2), ...]}
        n_clusters              : 每组默认的聚类数量（默认为3）
    Returns：
        results                 : 字典结构 {组名: [(原始索引, 簇标签), ...]}，每个元素保留原始索引和对应的簇标签

    """
    results = {}

    for group_name, items in grouped_data.items():
        # 提取原始索引和对应文本
        indices = [item[0] for item in items]
        contexts = [item[1] for item in items]

        if len(contexts) < 2:  # 如果组内只有一个样本，不需要聚类
            # 单个样本直接标记为簇0，保留原始索引
            results[group_name] = [(indices[0], 0)]
            continue

        try:
            # 使用TF-IDF向量化（限制1000维特征）
            vectorizer = TfidfVectorizer(max_features=1000)
            X = vectorizer.fit_transform(contexts)

            # 确定合理的簇数（不超过样本数-1）
            actual_n_clusters = min(n_clusters, len(contexts) - 1)
            # 至少1簇
            if actual_n_clusters <= 0:
                actual_n_clusters = 1
            # 执行聚类
            kmeans = KMeans(n_clusters=actual_n_clusters, random_state=42)  # 固定随机种子保证可重复性
            clusters = kmeans.fit_predict(X)  # 获取每个样本的簇标签

            # 存储结果 (原始索引, 簇标签)
            results[group_name] = list(zip(indices, clusters))
        except ValueError as e:
            print(f"Error processing group {group_name}: {e}")
            # 出错时默认所有样本归为簇0，保留原始索引
            results[group_name] = [(idx, 0) for idx in indices]

    return results


# 标签聚类分析
def cluster4tags(file_path):
    """
    跟据提供的标签与对应清洗数据分层进行聚类分析

    Args:
        file_path (str)         : 清洗数据文件路径
    Returns:
        df (DataFrame)          : 聚类分析结果df

    """
    # 1. 加载数据
    df = load_and_preprocess_data(file_path)
    df = df.reset_index(drop=True)
    # 2. 聚类1: 对所有行进行聚类
    print("\nClustering 1: Cluster all rows together initializing ...")
    all_clusters = cluster_all_rows(df['Context'])
    df['Cluster_All'] = all_clusters
    # print(df.groupby('Cluster_All').size())

    # 3. 聚类2: 按中间标签分组后再聚类
    print("\nClustering 2: Cluster within middle label groups initializing ...")
    middle_label_groups = cluster_by_middle_label(df)
    middle_label_results = perform_clustering_within_groups(middle_label_groups)

    # 将结果添加到DataFrame
    cluster_middle = np.zeros(len(df), dtype=int)
    for group, items in middle_label_results.items():
        for idx, cluster in items:
            cluster_middle[idx] = cluster
    df['Cluster_MiddleLabel'] = cluster_middle
    # print(df.groupby('Cluster_MiddleLabel').size())

    # 4. 聚类3: 按完整标签分组后再聚类
    print("\nClustering 3: Cluster within full label groups initializing ...")
    full_label_groups = cluster_by_full_label(df)
    full_label_results = perform_clustering_within_groups(full_label_groups)

    # 将结果添加到DataFrame
    cluster_full = np.zeros(len(df), dtype=int)
    for group, items in full_label_results.items():
        for idx, cluster in items:
            cluster_full[idx] = cluster
    df['Cluster_FullLabel'] = cluster_full
    # print(df.groupby('Cluster_FullLabel').size())
    return df


# 处理并保存聚类分析结果
def cluster_analysis(input_file, output_name):
    file = input_file
    result_df = cluster4tags(file)
    result_df.to_csv(f"""./bunkr/{output_name}_clustering_results.csv""", index=False)


# --- 数据清洗 ---
# 清除非中文字符
def clean_none_cn_text(text):
    cleaned = re.sub(r'[^\u4e00-\u9fff]', '', text)
    return cleaned


# 清洗符号等特殊字符(不包括英文)
def clean_special_char(text):
    # cleaned = re.sub(r'[^\u4e00-\u9fffa-zA-Z]', '', text)
    cleaned = re.sub(r'[^\u4e00-\u9fffa-zA-Z0-9]', '', text)
    return cleaned


# 匹配函数
def extract_util(merged_text, start_marker, end_marker):
    """
    跟据提供的起始marker和末端marker对上下文正则匹配

    Args:
        merged_text (str)       : 上下文
        start_marker            : 起始标志
        end_marker              : 末端标志
    Returns:
        extracted (str)         : 提取的字符串

    """
    # 查找最先出现的起始标记
    start_idx = -1

    temp_idx = merged_text.find(start_marker)
    if temp_idx != -1 and (start_idx == -1 or temp_idx < start_idx):
        start_idx = temp_idx
        start_marker = start_marker

    if start_idx == -1:
        return ""

    start_idx += len(start_marker)  # 移动至起始标记后

    if end_marker == '':
        extracted = merged_text[start_idx:]  # 无结束标记则取到末尾
    else:
        end_idx = merged_text.find(end_marker, start_idx)
        extracted = merged_text[start_idx:end_idx].strip()

    return extracted


# 分段正则匹配
def extract_content_distribution(text_main):
    """
    将工单上下文分段正则匹配提取训练集与验证集内容

    Args:
        text (str)              : 工单上下文
    Returns:
        extracted_result (str)  : 提取的字符串

    """
    if not text_main:
        return ""
    # 合并换行符为空格（根据需求可调整）
    text = str(text_main).replace("\n", "")

    main_context = ''

    # if start_marker_main_0 == False:
    if "工单立单提交|内容：" in text_main:
        main_context = extract_util(text, "工单立单提交|内容：", "处理人")
    # elif start_marker_main_1 == False:
    elif "虚拟工单立单提交" in text_main:
        main_context = extract_util(text, "虚拟工单立单提交", "处理人")
    else:
        # print('No Valid Marker for Main Context Found!!!')
        i = 1

    internal_reply_context = ''
    # 匹配所有内部回复的详情内容
    # internal_reply_matches = re.findall(r'内部回复[^|]*\|详情：(.*?)(?=处理人:|$)', text)
    internal_reply_matches = re.findall(
        r'内部回复\|处理时间：\d{4}/\d{2}/\d{2} \d{2}:\d{2}:\d{2}\|详情：(.*?)(?=处理人:|$)', text, re.S)

    if internal_reply_matches:
        # 检查第一个内部回复内容是否需要去重
        first_reply = internal_reply_matches[0].strip()
        if len(first_reply) > 20 and first_reply in main_context:
            # 跳过第一个回复（已包含在主内容中）
            internal_reply_matches = internal_reply_matches[1:]

        # 拼接所有有效的内部回复内容
        internal_reply_context = ''.join(internal_reply_matches)

    subcontext_3 = ''
    if "返单" in text_main:
        subcontext_3 = extract_util(text, "返单", "处理人")

    sub_context_0 = ''
    # if (start_marker_sub_0 == False) and (end_marker_sub_0 == False):
    if "一、处理结果" in text_main:
        sub_context_0 = extract_util(text, "一、处理结果", "三、")
    # elif (start_marker_sub_1 == False) and (end_marker_sub_0 == False):
    elif "一、处理情况" in text_main:
        sub_context_0 = extract_util(text, "一、处理情况", "三、")
    else:
        # print('No Valid Marker for Sub Context 0 Found!!!')
        i = 1

    sub_context_1 = ''
    # if (start_marker_sub_2 == False) and (end_marker_sub_1 == False):
    if "2、核实情况" in text_main:
        sub_context_1 = extract_util(text, "2、核实情况", "处理人员姓名")

    sub_context_2 = ''
    # 当且仅当工单内容没有核实、核查的关键词，否则数据集会重复
    # if (start_marker_sub_3 == False) and (start_marker_sub_2 == True) and (start_marker_sub_1 == True) and (start_marker_sub_0 == True):
    if ("归档" in text_main) and ("2、核实情况" not in text_main) and ("一、处理情况" not in text_main) and (
            "一、处理结果" not in text_main):
        sub_context_2 = extract_util(text, "归档", '')
    else:
        # print('No Valid Marker for Sub Context 1 Found!!!')
        i = 1

    extracted_result = main_context + internal_reply_context + subcontext_3 + sub_context_0 + sub_context_1 + sub_context_2

    return extracted_result


# 处理标签库
def process_product_labels(file_path):
    """
    读取xlsx标签库文件，格式化标签链

    Args:
        file_path (str)         : xlsx格式的excel表格
    Returns:
        processed_data (List)   : 字典列表
        {
            'original': line,
            'segments': parts,
            'num_segments': num_segments,
            'empty_segments': empty_segments
        }                       : 字典
    """
    with open(file_path, 'r', encoding='utf-8') as file:
        lines = file.readlines()

    processed_data = []
    for line in lines:
        line = line.strip()  # 去除首尾空白字符
        if not line:
            continue  # 跳过空行

        # 分割字符串并保留空字段
        parts = line.split('-')
        result_csv = ""
        for i in parts:
            result_csv = result_csv + i + ','
        print(f"""Current Tag: {result_csv}""")
        # 添加统计信息
        num_segments = len(parts)
        empty_segments = sum(1 for segment in parts if segment == '')

        # 保留原始数据处理结果
        processed_data.append({
            'original': line,
            'segments': parts,
            'num_segments': num_segments,
            'empty_segments': empty_segments
        })

    return processed_data


# 处理并保存标签库
def process_tag():
    product_data = process_product_labels('./bunkr/tag.csv')
    for item in product_data:
        # print(f"原始标签: {item['original']}")
        # print(f"分割结果: {item['segments']}")
        # print(f"分段数: {item['num_segments']}, 空段数: {item['empty_segments']}")
        with open('./bunkr/result.csv', 'a', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow(item['segments'])
        # print()


# 按照层级标签清洗数据
def process_training_tag_list(file_path, tag_list_input, output_name):
    """
    根据层级标签清洗工单上下文内容

    Args:
        file_path (str)         : xlsx格式的excel表格
        tag_list_input (List)   : 需要清洗的层级标签列表
        output_name (str)       : 清洗数据另存为命名
    Returns:
        None
    """
    tag_list = tag_list_input

    # 加载 Excel 文件
    workbook = load_workbook(file_path, data_only=True)
    for tag in tag_list:
        sheet = workbook[tag]
        # 存储结果的列表（每个元素是列的元组）
        results = []
        # 遍历从第二行开始的每一行（openpyxl 行号从 1 开始）
        for row in sheet.iter_rows(min_row=2, min_col=1, max_col=2, values_only=True):
            tag_value, context_value = row  # 解包所有列的值
            processed_context_general = extract_content_distribution(context_value)
            processed_context_additional = clean_special_char(processed_context_general)
            results.append((tag_value, processed_context_additional))
        # 打印提取结果
        # print("提取到的数据：")
        with open(f'''./bunkr/{output_name}_clean_new.csv''', 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            for idx, (e, f) in enumerate(results, start=3):
                # print(f"第 {idx} 行 | 标签: {e} | 内容: {f}")
                writer.writerow([f"""{e}""", f"""{f}"""])


# EXCEL预处理数据清洗与挖掘
def data_mining_cluster(excel_file_path, tag_list, tag_department, keyword_file_path, training_file_path):
    process_training_tag_list(excel_file_path, tag_list, tag_department)
    # keyword4tag(keyword_file_path,'all')
    bge_training_data_cluster(keyword_file_path, training_file_path)


# --- Utilities ---
# --- 时间戳格式化 ---
def format_runtime(seconds):
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    seconds = int(seconds % 60)
    return f"{hours:02d}h:{minutes:02d}m:{seconds:02d}s"


# 按层级查找标签
def query_by_level(level):
    conn = sqlite3.connect('./bunkr/labels.db')
    cursor = conn.cursor()
    cursor.execute('SELECT label FROM nodes WHERE level = ?', (level,))
    return [row[0] for row in cursor.fetchall()]


# 按祖先路径查找子标签
def query_by_ancestor(ancestor_path, target_level):
    # 先找到祖先路径对应的节点ID
    conn = sqlite3.connect('./bunkr/labels.db')
    cursor = conn.cursor()
    cursor.execute('SELECT id, level FROM nodes WHERE path = ?', (ancestor_path,))
    ancestor = cursor.fetchone()
    if not ancestor:
        return []

    ancestor_id, ancestor_level = ancestor
    # 目标层级必须大于祖先层级
    if target_level <= ancestor_level:
        return []

    # 查找所有父节点为ancestor_id且层级为target_level的标签
    cursor.execute('''
                   SELECT label
                   FROM nodes
                   WHERE parent_id = ?
                     AND level = ?
                   ''', (ancestor_id, target_level))
    return [row[0] for row in cursor.fetchall()]


# 按祖先路径查找规则
def query_rule_by_ancestor(path, tag):
    conn = sqlite3.connect('./bunkr/labels.db')
    cursor = conn.cursor()
    cursor.execute('SELECT rule FROM nodes WHERE path = ? AND label = ?', (path, tag,))
    return [row[0] for row in cursor.fetchall()]


# 按祖先路径查找关键词
def query_keywords_by_ancestor(path, tag):
    conn = sqlite3.connect('./bunkr/labels.db')
    cursor = conn.cursor()
    cursor.execute('SELECT keywords FROM nodes WHERE path = ? AND label = ?', (path, tag,))
    return [row[0] for row in cursor.fetchall()]


def query_parent_of_level5(label_or_path):
    """根据第五级节点查找其直属上层(第四级)节点

    Args:
        label_or_path: 第五级节点的标签或完整路径

    Returns:
        返回第四级节点的标签，如果没有找到则返回None
    """
    conn = sqlite3.connect('./bunkr/labels.db')
    cursor = conn.cursor()

    # 先找到第五级节点的信息
    cursor.execute('''
                   SELECT parent_id
                   FROM nodes
                   WHERE (label = ? OR path = ?)
                     AND level = 5
                   ''', (label_or_path, label_or_path))
    result = cursor.fetchone()

    if not result:
        return None

    parent_id = result[0]

    # 查找父节点(应该是第四级)
    cursor.execute('''
                   SELECT label
                   FROM nodes
                   WHERE id = ?
                     AND level = 4
                   ''', (parent_id,))
    parent_result = cursor.fetchone()

    return parent_result[0] if parent_result else None


def parse_db_similarity(raw_data):
    result = []
    if raw_data:
        main_string = raw_data[0]
        result = main_string.split('|')
        return result
    return result


def parse_field(field):
    """解析大括号包裹的分层数据结构"""
    result = []
    if field.startswith('{') and field.endswith('}'):
        content = field[1:-1].strip()
        parts = []
        current = []
        depth = 0

        for char in content:
            if char == '[':
                depth += 1
            elif char == ']':
                depth -= 1

            if char == '|' and depth == 0:
                parts.append(''.join(current).strip())
                current = []
            else:
                current.append(char)

        if current:
            parts.append(''.join(current).strip())

        for part in parts:
            if part.startswith('[') and part.endswith(']'):
                inner_content = part[1:-1].strip()
                if inner_content:
                    # 移除单引号并分割内层数据
                    items = [item.strip()[1:-1] for item in inner_content.split('|')
                             if item.strip()]
                    result.append('|'.join(items))
                else:
                    result.append('')
            else:
                result.append(part)

    # 确保始终返回5个元素
    while len(result) < 5:
        result.append('')

    return result[:5]


def init_db():
    if not os.path.exists('./bunkr/labels.db'):
        """
        关系数据库初始化

        Args:
            id            : 整数主键ID
            label         : 标签文本
            level         : 标签层级
            parent_id     : 父标签ID
            path          : 回溯到根标签路径
            rule          : 该标签涉及规则（用于语义计算）
            keywords      : 该标签涉及关键词（用于直接检索）
        """
        conn = sqlite3.connect('./bunkr/labels.db')
        cursor = conn.cursor()

        cursor.execute('''
                       CREATE TABLE IF NOT EXISTS nodes
                       (
                           id
                           INTEGER
                           PRIMARY
                           KEY,
                           label
                           TEXT
                           NOT
                           NULL,
                           level
                           INTEGER
                           NOT
                           NULL,
                           parent_id
                           INTEGER,
                           path
                           TEXT
                           NOT
                           NULL
                           UNIQUE,
                           rule
                           TEXT,
                           keywords
                           TEXT
                       )
                       ''')
        conn.commit()

        with open('./bunkr/标签库.csv', 'r', newline='', encoding='utf-8') as file:
            reader = csv.reader(file)
            for row in reader:
                prev_node_id = None
                prev_level = 0

                if len(row) < 7:
                    row.extend([''] * (7 - len(row)))

                # 分离主标签和规则/关键词
                main_tags = row[:5]  # 层级标签
                rule_tag = row[5]  # 规则集
                keyword_tag = row[6]  # 关键词集

                # 解析规则标签
                rule_tags = parse_field(rule_tag)
                # 解析关键词标签
                keyword_tags = parse_field(keyword_tag)

                for index, raw_label in enumerate(main_tags):
                    label = raw_label.strip()
                    if not label:
                        continue  # 跳过空标签

                    current_level = index + 1
                    if current_level <= prev_level:
                        print(f"层级顺序错误，跳过此行: {row}")
                        break
                    # 当前标签规则/关键词提取
                    current_rule = rule_tags[index] if index < len(rule_tags) else ''
                    current_keywords = keyword_tags[index] if index < len(keyword_tags) else ''

                    # 构建当前路径
                    if prev_node_id is not None:
                        cursor.execute('SELECT path FROM nodes WHERE id = ?', (prev_node_id,))
                        parent_path = cursor.fetchone()[0]
                        current_path = f"{parent_path}/{label}"
                    else:
                        current_path = label

                    # 检查是否已存在该路径
                    cursor.execute('SELECT id FROM nodes WHERE path = ?', (current_path,))
                    existing = cursor.fetchone()

                    if existing:
                        current_node_id = existing[0]
                        cursor.execute('SELECT rule FROM nodes WHERE id = ?', (current_node_id,))
                        existing_rule = cursor.fetchone()
                        cursor.execute('SELECT keywords FROM nodes WHERE id = ?', (current_node_id,))
                        existing_keywords = cursor.fetchone()
                        if existing_rule[0] == '':
                            cursor.execute('''
                                           UPDATE nodes
                                           SET rule = ?
                                           WHERE id = ?
                                           ''', (current_rule, current_node_id))
                        if existing_keywords[0] == '':
                            cursor.execute('''
                                           UPDATE nodes
                                           SET keywords = ?
                                           WHERE id = ?
                                           ''', (current_keywords, current_node_id))
                    else:
                        cursor.execute('''
                                       INSERT INTO nodes (label, level, parent_id, path, rule, keywords)
                                       VALUES (?, ?, ?, ?, ?, ?)
                                       ''', (label, current_level, prev_node_id, current_path, current_rule,
                                             current_keywords))
                        current_node_id = cursor.lastrowid

                    prev_node_id = current_node_id
                    prev_level = current_level
        conn.commit()
        conn.close()


if __name__ == "__main__":
    tag_list_app_room = [
        '流量业务',
        '充值业务',
        '号卡业务',
        '权益业务',
        '直连卫星',
        '营销活动业务',
        '携号转网',
        '跨域融合业务',
        'APP业务',
        'APP功能',
        '星级服务',
        '终端业务',
        'OAO聚合页业务',
        '营销活动'
    ]

    # 生产交付室
    tag_list_production_deliver = [
        '携号转网',
        '号卡业务',
        '卡速达',
        '客服服务问题',
        '跨域融合业务',
        '星卡',
        '5G畅享套餐',
        '其他',
        '无忧卡'
    ]

    # 互联网发展室
    tag_list_internet_development = [
        '流量业务',
        '号卡业务',
        '权益业务',
        '天翼小白业务',
        '合约业务'
    ]

    # 智能终端运营室
    tag_list_terminal_operation = [
        '流量业务',
        '权益业务',
        '终端业务'
    ]

    # 数字门店及社乡运营室
    tag_list_digital_retail = [
        'eSIM业务',
        '充值业务',
        'OAO聚合页业务',
        '营销活动',
        '流量业务'
    ]

    # 销服与战新业务拓展室
    tag_list_sales_strategy = [
        '流量业务'
    ]

    # 销服与战新业务拓展室
    tag_list_demo = [
        '生产交付室-号卡业务-无忧卡',
        'APP运营室-APP功能-电子发票'
    ]

    tag_list_demo_0 = [
        '生产交付室-号卡业务-无忧卡'
    ]

    tag_list_demo_1 = [
        'APP运营室-APP功能-电子发票'
    ]

    print("所有一级标签:", query_by_level(1))

    t4gger4040('./output/sampled_data.xlsx', ['系统需求'], 'sampled_data_output')


